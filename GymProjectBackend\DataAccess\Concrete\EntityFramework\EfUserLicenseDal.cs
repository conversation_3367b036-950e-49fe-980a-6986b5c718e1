using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.EntityFrameworkCore;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserLicenseDal : EfEntityRepositoryBase<UserLicense, GymContext>, IUserLicenseDal
    {
        public EfUserLicenseDal(GymContext context) : base(context)
        {
        }

        public List<UserLicenseDto> GetUserLicenseDetails()
        {
            var now = DateTime.Now;
            var result = from ul in _context.UserLicenses
                         join u in _context.Users on ul.UserID equals u.UserID
                         join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                         join uc in _context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                         from uc in ucGroup.DefaultIfEmpty()
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                         from c in cGroup.DefaultIfEmpty()
                         where ul.IsActive && ul.EndDate >= now
                         select new UserLicenseDto
                         {
                             UserLicenseID = ul.UserLicenseID,
                             UserID = ul.UserID,
                             UserName = u.FirstName + " " + u.LastName,
                             UserEmail = u.Email,
                             CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                             LicensePackageID = ul.LicensePackageID,
                             PackageName = lp.Name,
                             Role = lp.Role,
                             StartDate = ul.StartDate,
                             EndDate = ul.EndDate,
                             RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                             IsActive = ul.IsActive
                         };
            return result.ToList();
        }

        public List<UserLicenseDto> GetActiveUserLicensesByUserId(int userId)
        {
            var now = DateTime.Now;
            var result = from ul in _context.UserLicenses
                         join u in _context.Users on ul.UserID equals u.UserID
                         join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                         join uc in _context.UserCompanies on u.UserID equals uc.UserID
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID
                         where ul.UserID == userId && ul.IsActive && ul.EndDate >= now
                         select new UserLicenseDto
                         {
                             UserLicenseID = ul.UserLicenseID,
                             UserID = ul.UserID,
                             UserName = u.FirstName + " " + u.LastName,
                             UserEmail = u.Email,
                             CompanyName = c.CompanyName,
                             LicensePackageID = ul.LicensePackageID,
                             PackageName = lp.Name,
                             Role = lp.Role,
                             StartDate = ul.StartDate,
                             EndDate = ul.EndDate,
                             RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                             IsActive = ul.IsActive
                         };

            return result.ToList();
        }

        public UserLicenseDto GetUserLicenseDetail(int userLicenseId)
        {
            var now = DateTime.Now;
            var result = from ul in _context.UserLicenses
                         join u in _context.Users on ul.UserID equals u.UserID
                         join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                         join uc in _context.UserCompanies on u.UserID equals uc.UserID
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID
                         where ul.UserLicenseID == userLicenseId
                         select new UserLicenseDto
                         {
                             UserLicenseID = ul.UserLicenseID,
                             UserID = ul.UserID,
                             UserName = u.FirstName + " " + u.LastName,
                             UserEmail = u.Email,
                             CompanyName = c.CompanyName,
                             LicensePackageID = ul.LicensePackageID,
                             PackageName = lp.Name,
                             Role = lp.Role,
                             StartDate = ul.StartDate,
                             EndDate = ul.EndDate,
                             RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                             IsActive = ul.IsActive
                         };

            return result.FirstOrDefault();
        }

        public PaginatedUserLicenseDto GetUserLicenseDetailsPaginated(int page, int pageSize, string searchTerm, string sortBy, string companyName, int? remainingDaysMin, int? remainingDaysMax)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses
                       join u in _context.Users on ul.UserID equals u.UserID
                       join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                       join uc in _context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                       from uc in ucGroup.DefaultIfEmpty()
                       join c in _context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                       from c in cGroup.DefaultIfEmpty()
                       where ul.IsActive && ul.EndDate >= now
                       select new UserLicenseDto
                       {
                           UserLicenseID = ul.UserLicenseID,
                           UserID = ul.UserID,
                           UserName = u.FirstName + " " + u.LastName,
                           UserEmail = u.Email,
                           CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                           LicensePackageID = ul.LicensePackageID,
                           PackageName = lp.Name,
                           Role = lp.Role,
                           StartDate = ul.StartDate,
                           EndDate = ul.EndDate,
                           RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                           IsActive = ul.IsActive
                       };

            var totalCount = query.Count();
            var items = query.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return new PaginatedUserLicenseDto
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }

        public PaginatedUserLicenseDto GetExpiredAndPassiveLicenses(int page, int pageSize, string searchTerm)
        {
            var now = DateTime.Now;
            var query = from ul in _context.UserLicenses
                       join u in _context.Users on ul.UserID equals u.UserID
                       join lp in _context.LicensePackages on ul.LicensePackageID equals lp.LicensePackageID
                       join uc in _context.UserCompanies on u.UserID equals uc.UserID into ucGroup
                       from uc in ucGroup.DefaultIfEmpty()
                       join c in _context.Companies on uc.CompanyId equals c.CompanyID into cGroup
                       from c in cGroup.DefaultIfEmpty()
                       where (!ul.IsActive || ul.EndDate < now)
                       select new UserLicenseDto
                       {
                           UserLicenseID = ul.UserLicenseID,
                           UserID = ul.UserID,
                           UserName = u.FirstName + " " + u.LastName,
                           UserEmail = u.Email,
                           CompanyName = c != null ? c.CompanyName : "Şirket Atanmamış",
                           LicensePackageID = ul.LicensePackageID,
                           PackageName = lp.Name,
                           Role = lp.Role,
                           StartDate = ul.StartDate,
                           EndDate = ul.EndDate,
                           RemainingDays = EF.Functions.DateDiffDay(now, ul.EndDate),
                           IsActive = ul.IsActive
                       };

            // Search term filtresi
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(x => x.UserName.Contains(searchTerm) ||
                                        x.UserEmail.Contains(searchTerm) ||
                                        x.CompanyName.Contains(searchTerm));
            }

            var totalCount = query.Count();
            var items = query.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            return new PaginatedUserLicenseDto
            {
                Items = items,
                TotalCount = totalCount,
                Page = page,
                PageSize = pageSize
            };
        }
    }
}
